<?php

declare(strict_types=1);

namespace app\tests\functional\api;

use app\back\entities\UserContact;
use app\back\entities\UserTransaction;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\TestCase;

class AvailableSiteIdsTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    public function testAllowedSiteId(): void
    {
        /** set in back/config/env/test/api_clients.php */
        $allowedSiteId = 7;

        $d3Ago = (new \DateTimeImmutable('-3 days'))->setTime(0, 0);
        $this->haveRates($d3Ago->format('Y-m-d'));
        $transactionId = (string)self::uniqRuntimeId();

        $this->haveUserTransactionRecord([
            'site_id' => $allowedSiteId,
            'transaction_id' => $transactionId,
            'op_id' => UserTransaction::OP_OUT,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'dir' => UserTransaction::DIR_OUT,
            'status' => UserTransaction::STATUS_NEW,
            'withdraw_approved_at' => null,
            'created_at' => $d3Ago,
        ]);

        $this->sendAPI('yhelper', 'put/withdrawal-approve', [
            'site_id' => $allowedSiteId,
            'invoice_id' => $transactionId,
        ], 200);
    }

    public function testNotAllowedSiteId(): void
    {
        $siteIdNotAllowed = 28;
        $transactionId = (string)self::uniqRuntimeId();

        $this->sendAPI('yhelper', 'put/withdrawal-approve', [
            'site_id' => $siteIdNotAllowed,
            'invoice_id' => $transactionId,
        ], 422);

        $this->seeResponseContains('site_id 28 is not allowed');
    }

    public function testNotExistsSiteId(): void
    {
        $siteIdNotExists = 1228;
        $email = '<EMAIL>';
        $user_id = self::uniqRuntimeId();

        $this->sendAPI('crm', 'put/users-contacts', [
            'type' => UserContact::TYPE_EMAIL,
            'value' => $email,
            'site_id' => $siteIdNotExists,
            'user_id' => $user_id,
            'relevancy' => UserContact::RELEVANCY_EMAIL
        ], 422);

        $this->seeResponseContains('site_id 1228 is not allowed');
    }
}
