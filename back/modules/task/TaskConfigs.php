<?php

declare(strict_types=1);

namespace app\back\modules\task;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Str;
use app\back\config\tasks\Res;
use app\back\modules\task\components\TaskInitException;

class TaskConfigs
{
    private array $tasksQueues;
    private array $tasksSchedule;
    private array $tasksAccess;

    public function getConfig(string $resource, string $taskName): array
    {
        $platform = Res::getPlatformByResource($resource);

        $platformConfig = $platform !== null ? $this->findConfigByResource($platform) : [];
        $resourceConfig = $this->findConfigByResource($resource);

        $tasksConfig = Arr::merge($platformConfig, $resourceConfig);

        $platformTaskConfig = $platform !== null ? $this->canonicalTaskConfig($tasksConfig, $platform, $taskName) : [];
        $resourceTaskConfig = $this->canonicalTaskConfig($tasksConfig, $resource, $taskName);

        $config = Arr::merge($platformTaskConfig, $resourceTaskConfig);

        if (empty($config)) {
            throw new TaskInitException("Unable to find config for $taskName $resource");
        }

        if (!array_key_exists('class', $config)) {
            throw new TaskInitException("Unable to find 'class' key for task $taskName $resource in config:\n" . Str::fancyAssocList($config));
        }

        $tasksAccessConfig = $this->getTasksAccess();
        if (array_key_exists($resource, $tasksAccessConfig) && array_key_exists('request', $config)) {
            $config['request'] = Arr::merge($tasksAccessConfig[$resource], $config['request']);
        }

        return $config;
    }

    private function canonicalTaskConfig(array $config, string $resource, string $taskName): array
    {
        if (!array_key_exists($resource, $config)) {
            return [];
        }

        $tasks = $config[$resource];

        if (!array_key_exists($taskName, $tasks)) {
            return [];
        }

        $task = $tasks[$taskName];

        if (is_scalar($task)) {
            $task = ['class' => $task];
        }

        return $task;
    }

    protected function findConfigByResource(string $resource): array // protected only for tests purpose (mock)
    {
        return match ($resource) {
            Res::ADS_FACEBOOK, Res::ADS_GOOGLE, Res::ADS_IRON, Res::ADS_UNITY, Res::ADS_VUNGLE, Res::ADS_MINTEGRAL => require APP_ROOT . 'back/config/tasks/resources/ads.php',
            Res::PLATFORM_BETTING, Res::BETTING => require APP_ROOT . 'back/config/tasks/resources/betting.php',
            Res::BUF => require APP_ROOT . 'back/config/tasks/resources/buf.php',
            Res::CHECK, Res::CHECK_CRITICAL => require APP_ROOT . 'back/config/tasks/resources/check.php',
            Res::CID => require APP_ROOT . 'back/config/tasks/resources/cid.php',
            Res::CRM => require APP_ROOT . 'back/config/tasks/resources/crm.php',
            Res::DEFAULT => require APP_ROOT . 'back/config/tasks/resources/default.php',
            Res::FACES => require APP_ROOT . 'back/config/tasks/resources/faces.php',
            Res::PLATFORM_GGATE => require APP_ROOT . 'back/config/tasks/resources/ggate.php',
            Res::PLATFORM_GI => require APP_ROOT . 'back/config/tasks/resources/gi.php',
            Res::PLATFORM_HHS => require APP_ROOT . 'back/config/tasks/resources/hhs.php',
            Res::L4P, Res::P4E => require APP_ROOT . 'back/config/tasks/resources/l4p.php',
            Res::PLATFORM_S2P => require APP_ROOT . 'back/config/tasks/resources/s2p.php',
            Res::SEND => require APP_ROOT . 'back/config/tasks/resources/send.php',
            Res::PLATFORM_SMEN => require APP_ROOT . 'back/config/tasks/resources/smen.php',
            Res::PLATFORM_STP, Res::STP_ADJUST => require APP_ROOT . 'back/config/tasks/resources/stp.php',
            Res::VIPAFF, Res::VIPAFF_CP => require APP_ROOT . 'back/config/tasks/resources/vipaff.php',
            Res::WP => require APP_ROOT . 'back/config/tasks/resources/wp.php',
            Res::YHLP => require APP_ROOT . 'back/config/tasks/resources/yhlp.php',
            Res::PLATFORM_YS, Res::PLATFORM_YS_SOFTSWISS, Res::PLATFORM_YS_RG => require APP_ROOT . 'back/config/tasks/resources/ys.php',
            default => require APP_ROOT . 'back/config/tasks/resources/other.php',
        };
    }

    public function getTasksActions(): array
    {
        static $tasksActions;

        if ($tasksActions === null) {
            $tasksSchedule = require APP_ROOT . 'back/config/env/' . APP_ENV . '/tasks_schedule.php';

            foreach ($tasksSchedule['schedule'] as $tasks) {
                $tasksActions[] = array_keys($tasks);
            }
            $tasksActions = array_merge(...$tasksActions);
            sort($tasksActions);
        }

        return $tasksActions;
    }

    public function getTasksQueues(): array
    {
        if (!isset($this->tasksQueues)) {
            $this->tasksQueues = require APP_ROOT . 'back/config/tasks/tasks_queues.php';
        }

        return $this->tasksQueues;
    }

    public function getTasksSchedule(): array
    {
        if (!isset($this->tasksSchedule)) {
            $this->tasksSchedule = require APP_ROOT . 'back/config/env/' . APP_ENV . '/tasks_schedule.php';
        }

        return $this->tasksSchedule;
    }

    public function getTasksAccess(): array
    {
        if (!isset($this->tasksAccess)) {
            $this->tasksAccess = require APP_ROOT . 'back/config/env/' . APP_ENV . '/tasks_access.php';
        }

        return $this->tasksAccess;
    }
}
